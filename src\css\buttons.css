/* 🎮 通用按钮样式 - Widget功能模块已移除 */

/* 通用按钮样式 */
.btn {
    background: linear-gradient(145deg, rgba(64, 224, 255, 0.2), rgba(30, 30, 50, 0.9));
    border: 1px solid rgba(64, 224, 255, 0.5);
    color: white;
    padding: 12px 24px;
    border-radius: 10px;
    cursor: pointer;
    text-decoration: none;
    display: inline-block;
    margin: 10px 5px;
    transition: all 0.3s ease;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    font-size: 14px;
    font-weight: 500;
}

.btn:hover {
    background: linear-gradient(145deg, rgba(64, 224, 255, 0.3), rgba(30, 30, 50, 0.95));
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(64, 224, 255, 0.3);
    text-decoration: none;
    color: white;
}

.btn:active {
    transform: translateY(0);
    box-shadow: 0 5px 10px rgba(64, 224, 255, 0.2);
}

/* 按钮变体 */
.btn-primary {
    background: linear-gradient(145deg, rgba(64, 224, 255, 0.3), rgba(30, 30, 50, 0.9));
    border-color: rgba(64, 224, 255, 0.8);
}

.btn-secondary {
    background: linear-gradient(145deg, rgba(100, 149, 237, 0.2), rgba(30, 30, 50, 0.9));
    border-color: rgba(100, 149, 237, 0.5);
}

.btn-success {
    background: linear-gradient(145deg, rgba(0, 255, 136, 0.2), rgba(30, 30, 50, 0.9));
    border-color: rgba(0, 255, 136, 0.5);
}

.btn-warning {
    background: linear-gradient(145deg, rgba(255, 170, 0, 0.2), rgba(30, 30, 50, 0.9));
    border-color: rgba(255, 170, 0, 0.5);
}

.btn-danger {
    background: linear-gradient(145deg, rgba(255, 87, 87, 0.2), rgba(30, 30, 50, 0.9));
    border-color: rgba(255, 87, 87, 0.5);
}

/* 小尺寸按钮 */
.btn-sm {
    padding: 8px 16px;
    font-size: 12px;
}

/* 大尺寸按钮 */
.btn-lg {
    padding: 16px 32px;
    font-size: 16px;
}

/* 禁用状态 */
.btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none !important;
    box-shadow: none !important;
}
