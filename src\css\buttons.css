/* 🎮 工具按钮样式已移除 - Widget功能模块不存在 */
/* 文件保留用于未来可能的按钮样式扩展 */

/* 通用按钮样式 */
.btn {
    background: linear-gradient(145deg, rgba(64, 224, 255, 0.2), rgba(30, 30, 50, 0.9));
    border: 1px solid rgba(64, 224, 255, 0.5);
    color: white;
    padding: 12px 24px;
    border-radius: 10px;
    cursor: pointer;
    text-decoration: none;
    display: inline-block;
    margin: 10px 5px;
    transition: all 0.3s ease;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.btn:hover {
    background: linear-gradient(145deg, rgba(64, 224, 255, 0.3), rgba(30, 30, 50, 0.95));
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(64, 224, 255, 0.3);
    text-decoration: none;
    color: white;
}