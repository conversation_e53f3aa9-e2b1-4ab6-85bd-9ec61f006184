<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>电子沙盘展示</title>
    
    <!-- Cesium CDN -->
    <link href="node_modules/cesium/Build/Cesium/Widgets/widgets.css" rel="stylesheet">
    <link rel="stylesheet" href="src/features/布局/坐标导航/navigation-combined.css">
    <!-- 自定义样式 -->
    <link rel="stylesheet" href="src/css/main.css">
    <link rel="stylesheet" href="src/css/title.css">
    <link rel="stylesheet" href="src/css/buttons.css">
    <!-- Widget样式已移除 -->
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    
    <!-- JavaScript -->
    <script src="node_modules/cesium/Build/Cesium/Cesium.js"></script>
    <script src="https://unpkg.com/@turf/turf@6.5.0/turf.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    <!-- 可选：PDF生成库 -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>

    <script src="src/js/cesium.js"></script>
    <script src="src/features/布局/天空盒/SkyBoxManager.js"></script>
    <script src="src/features/布局/坐标导航/CesiumNavigation.umd.js"></script>
    <script src="src/features/布局/坐标导航/CoordinateDisplay.js"></script>
</head>
<body>
    <!-- 标题图片容器 -->
    <div class="title-container">
        <img src="src/images/svg/title.svg" alt="标题">
        <div class="title-text">电子沙盘</div>
    </div>
    
    <!-- 🌟 超炫酷加载动画 -->
    <div id="loadingScreen" class="loading-screen">
        <div class="loading-content">
            <div class="loading-logo">
                <div class="loading-circle"></div>
                <div class="loading-text">电子沙盘</div>
            </div>
            <div class="loading-progress">
                <div class="progress-bar">
                    <div class="progress-fill"></div>
                </div>
                <div class="loading-status">正在初始化系统...</div>
            </div>
        </div>
        <div class="loading-particles"></div>
    </div>

    <div id="cesiumContainer"></div>
    
    <!-- 系统信息面板 -->
    <div id="systemInfo" class="system-info-panel">
        <div class="info-header">
            <i class="fa fa-info-circle"></i>
            <span>系统信息</span>
        </div>
        <div class="info-content">
            <div class="info-item">
                <span class="info-label">状态:</span>
                <span class="info-value" id="systemStatus">运行中</span>
            </div>
            <div class="info-item">
                <span class="info-label">版本:</span>
                <span class="info-value">v1.0.0</span>
            </div>
            <div class="info-item">
                <span class="info-label">引擎:</span>
                <span class="info-value">Cesium 1.111.0</span>
            </div>
            <div class="info-divider"></div>
            <div class="info-shortcuts">
                <div class="shortcuts-title">快捷键</div>
                <div class="shortcut-item">
                    <span class="shortcut-key">H</span>
                    <span class="shortcut-desc">显示/隐藏面板</span>
                </div>
                <div class="shortcut-item">
                    <span class="shortcut-key">R</span>
                    <span class="shortcut-desc">重置视角</span>
                </div>
                <div class="shortcut-item">
                    <span class="shortcut-key">F</span>
                    <span class="shortcut-desc">全屏切换</span>
                </div>
                <div class="shortcut-item">
                    <span class="shortcut-key">ESC</span>
                    <span class="shortcut-desc">隐藏所有面板</span>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 🔔 现代化通知系统 -->
    <div id="notificationContainer" class="notification-container"></div>

    <!-- 引入SVG图标 -->
    <div id="svg-container"></div>
    
    <script>
        // 🔔 现代化通知系统
        function showNotification(title, message, type = 'info', duration = 4000) {
            const container = document.getElementById('notificationContainer');
            const notification = document.createElement('div');
            notification.className = `notification ${type}`;

            notification.innerHTML = `
                <div class="notification-content">
                    <div class="notification-title">${title}</div>
                    <div>${message}</div>
                </div>
            `;

            container.appendChild(notification);

            // 自动移除通知
            setTimeout(() => {
                notification.classList.add('hide');
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 300);
            }, duration);
        }

        // 🚀 超炫酷加载管理
        let loadingProgress = 0;
        const loadingSteps = [
            '正在加载Cesium引擎...',
            '正在初始化地球...',
            '正在加载地形数据...',
            '正在初始化坐标系统...',
            '正在加载界面组件...',
            '系统初始化完成！'
        ];

        function updateLoadingProgress(step, progress) {
            const progressFill = document.querySelector('.progress-fill');
            const statusText = document.querySelector('.loading-status');

            if (progressFill) {
                progressFill.style.width = progress + '%';
            }
            if (statusText && loadingSteps[step]) {
                statusText.textContent = loadingSteps[step];
            }
        }

        function hideLoadingScreen() {
            const loadingScreen = document.getElementById('loadingScreen');
            if (loadingScreen) {
                setTimeout(() => {
                    loadingScreen.classList.add('hidden');
                    setTimeout(() => {
                        loadingScreen.style.display = 'none';
                    }, 800);
                }, 500);
            }
        }

        // 等待页面加载完成
        window.onload = function() {
            try {
                // 模拟加载进度
                updateLoadingProgress(0, 20);

                setTimeout(() => {
                    updateLoadingProgress(1, 40);
                    // 初始化Cesium
                    const viewer = initCesium();
                
                    updateLoadingProgress(2, 60);

                    setTimeout(() => {
                        // 初始化坐标显示功能
                        window.coordinateDisplay = new CoordinateDisplay(viewer);
                        updateLoadingProgress(3, 70);

                        setTimeout(() => {
                            // 完成界面组件初始化
                            console.log('✅ 界面组件初始化完成');
                            showNotification('系统初始化', '界面组件已就绪', 'success', 2000);
                            updateLoadingProgress(4, 85);

                            setTimeout(() => {
                                console.log('✅ 系统初始化完成');
                                updateLoadingProgress(5, 100);

                                setTimeout(() => {
                                    hideLoadingScreen();
                                    showNotification('🎉 欢迎使用', '电子沙盘系统已成功启动！', 'success', 5000);

                                    // 延迟显示快捷键提示
                                    setTimeout(() => {
                                        showNotification('💡 提示', '按 H 键显示/隐藏系统信息面板', 'info', 4000);
                                    }, 3000);
                                }, 800);
                            }, 500);
                        }, 500);
                    }, 500);
                }, 500);
                
                // 加载SVG图标
                fetch('src/images/svg/icons.svg')
                    .then(response => {
                        if (!response.ok) {
                            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                        }
                        return response.text();
                    })
                    .then(svgContent => {
                        document.getElementById('svg-container').innerHTML = svgContent;
                        console.log('✅ SVG图标加载完成');
                    })
                    .catch(error => {
                        console.warn('SVG图标加载失败:', error);
                        // 创建一个简单的占位符SVG
                        document.getElementById('svg-container').innerHTML = `
                            <svg style="display: none;">
                                <defs>
                                    <symbol id="icon-placeholder" viewBox="0 0 24 24">
                                        <circle cx="12" cy="12" r="10" fill="#40E0FF" opacity="0.5"/>
                                    </symbol>
                                </defs>
                            </svg>
                        `;
                    });
                
            } catch (error) {
                console.error('初始化失败:', error);
            }
        };
        
        // 🎵 音效反馈系统（可选）
        function playClickSound() {
            try {
                // 创建简单的点击音效
                const audioContext = new (window.AudioContext || window.webkitAudioContext)();
                const oscillator = audioContext.createOscillator();
                const gainNode = audioContext.createGain();

                oscillator.connect(gainNode);
                gainNode.connect(audioContext.destination);

                oscillator.frequency.setValueAtTime(800, audioContext.currentTime);
                oscillator.frequency.exponentialRampToValueAtTime(400, audioContext.currentTime + 0.1);

                gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
                gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.1);

                oscillator.start(audioContext.currentTime);
                oscillator.stop(audioContext.currentTime + 0.1);
            } catch (e) {
                // 静默处理音效错误
            }
        }

        // ⌨️ 键盘快捷键系统
        document.addEventListener('keydown', function(event) {
            // 避免在输入框中触发快捷键
            if (event.target.tagName === 'INPUT' || event.target.tagName === 'TEXTAREA') {
                return;
            }

            switch(event.key.toLowerCase()) {
                case 'h': // 显示/隐藏帮助信息
                    toggleSystemInfo();
                    break;
                case 'r': // 重置视角
                    resetCameraView();
                    break;
                case 'f': // 全屏切换
                    toggleFullscreen();
                    break;
                case 'escape': // ESC键隐藏所有面板
                    hideAllPanels();
                    break;
            }
        });

        // 切换系统信息面板显示
        function toggleSystemInfo() {
            const panel = document.getElementById('systemInfo');
            if (panel.style.display === 'none') {
                panel.style.display = 'block';
                showNotification('快捷键', '系统信息面板已显示', 'info', 2000);
            } else {
                panel.style.display = 'none';
                showNotification('快捷键', '系统信息面板已隐藏', 'info', 2000);
            }
        }

        // 重置相机视角
        function resetCameraView() {
            if (window.viewer) {
                window.viewer.camera.flyTo({
                    destination: Cesium.Cartesian3.fromDegrees(104.0, 30.0, 17000000),
                    orientation: {
                        heading: 0.0,
                        pitch: -Cesium.Math.PI_OVER_TWO,
                        roll: 0.0
                    },
                    duration: 2.0
                });
                showNotification('快捷键', '视角已重置', 'success', 2000);
            }
        }

        // 全屏切换
        function toggleFullscreen() {
            if (!document.fullscreenElement) {
                document.documentElement.requestFullscreen().then(() => {
                    showNotification('快捷键', '已进入全屏模式', 'success', 2000);
                });
            } else {
                document.exitFullscreen().then(() => {
                    showNotification('快捷键', '已退出全屏模式', 'info', 2000);
                });
            }
        }

        // 隐藏所有面板
        function hideAllPanels() {
            const panels = document.querySelectorAll('.system-info-panel, .notification');
            panels.forEach(panel => {
                if (panel.classList.contains('notification')) {
                    panel.classList.add('hide');
                } else {
                    panel.style.display = 'none';
                }
            });
            showNotification('快捷键', '所有面板已隐藏', 'info', 1500);
        }
    </script>
</body>
</html>
