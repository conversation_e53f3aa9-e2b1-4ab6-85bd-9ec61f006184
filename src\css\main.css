/* 🌟 超现代化基础样式 */
body {
    margin: 0;
    padding: 0;
    overflow: hidden;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    height: 100vh;
    width: 100vw;
    background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
    position: relative;
}

/* 🌌 背景粒子效果 */
body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(circle at 20% 80%, rgba(64, 224, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(100, 149, 237, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(64, 224, 255, 0.05) 0%, transparent 50%);
    pointer-events: none;
    z-index: -1;
    animation: backgroundPulse 10s ease-in-out infinite alternate;
}

@keyframes backgroundPulse {
    0% { opacity: 0.3; }
    100% { opacity: 0.6; }
}

#cesiumContainer {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border-radius: 0;
    overflow: hidden;
    box-shadow: inset 0 0 100px rgba(0, 0, 0, 0.3);
}

/* 隐藏Cesium默认控件 */
.cesium-viewer-bottom,
.cesium-toolbar-button {
    display: none !important;
}

/* SVG容器 */
#svg-container {
    display: none;
}

/* 🚀 超炫酷加载动画 */
.loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg,
        #0a0a0a 0%,
        #1a1a2e 25%,
        #16213e 50%,
        #1a1a2e 75%,
        #0a0a0a 100%
    );
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    overflow: hidden;
    animation: loadingBg 3s ease-in-out infinite alternate;
}

@keyframes loadingBg {
    0% { background-position: 0% 50%; }
    100% { background-position: 100% 50%; }
}

.loading-content {
    text-align: center;
    z-index: 2;
    position: relative;
}

.loading-logo {
    margin-bottom: 40px;
    position: relative;
}

.loading-circle {
    width: 120px;
    height: 120px;
    border: 3px solid rgba(64, 224, 255, 0.2);
    border-top: 3px solid #40E0FF;
    border-radius: 50%;
    margin: 0 auto 20px;
    animation: spin 2s linear infinite, glow 3s ease-in-out infinite alternate;
    position: relative;
}

.loading-circle::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 80px;
    height: 80px;
    border: 2px solid rgba(100, 149, 237, 0.3);
    border-bottom: 2px solid #6495ED;
    border-radius: 50%;
    transform: translate(-50%, -50%);
    animation: spin 1.5s linear infinite reverse;
}

.loading-circle::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 40px;
    height: 40px;
    background: radial-gradient(circle, #40E0FF, #6495ED);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    animation: pulse 2s ease-in-out infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes glow {
    0% { box-shadow: 0 0 20px rgba(64, 224, 255, 0.3); }
    100% { box-shadow: 0 0 40px rgba(64, 224, 255, 0.8); }
}

@keyframes pulse {
    0%, 100% { transform: translate(-50%, -50%) scale(1); opacity: 1; }
    50% { transform: translate(-50%, -50%) scale(1.2); opacity: 0.8; }
}

.loading-text {
    font-size: 36px;
    font-weight: 900;
    color: #40E0FF;
    text-shadow:
        0 0 20px rgba(64, 224, 255, 0.8),
        0 0 40px rgba(64, 224, 255, 0.4);
    letter-spacing: 8px;
    font-family: 'Segoe UI', sans-serif;
    animation: textGlow 3s ease-in-out infinite alternate;
}

@keyframes textGlow {
    0% {
        text-shadow:
            0 0 20px rgba(64, 224, 255, 0.8),
            0 0 40px rgba(64, 224, 255, 0.4);
    }
    100% {
        text-shadow:
            0 0 30px rgba(64, 224, 255, 1),
            0 0 60px rgba(64, 224, 255, 0.6);
    }
}

.loading-progress {
    margin-top: 40px;
}

.progress-bar {
    width: 300px;
    height: 4px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 2px;
    margin: 0 auto 20px;
    overflow: hidden;
    position: relative;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #40E0FF, #6495ED, #40E0FF);
    background-size: 200% 100%;
    border-radius: 2px;
    width: 0%;
    animation: progressFill 3s ease-in-out forwards, progressShine 2s ease-in-out infinite;
}

@keyframes progressFill {
    0% { width: 0%; }
    100% { width: 100%; }
}

@keyframes progressShine {
    0% { background-position: -200% 0; }
    100% { background-position: 200% 0; }
}

.loading-status {
    color: rgba(255, 255, 255, 0.8);
    font-size: 16px;
    font-weight: 500;
    animation: statusFade 2s ease-in-out infinite alternate;
}

@keyframes statusFade {
    0% { opacity: 0.6; }
    100% { opacity: 1; }
}

.loading-particles {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(circle at 20% 20%, rgba(64, 224, 255, 0.1) 1px, transparent 1px),
        radial-gradient(circle at 80% 80%, rgba(100, 149, 237, 0.1) 1px, transparent 1px),
        radial-gradient(circle at 40% 60%, rgba(64, 224, 255, 0.05) 1px, transparent 1px);
    background-size: 50px 50px, 80px 80px, 60px 60px;
    animation: particlesFloat 20s linear infinite;
}

@keyframes particlesFloat {
    0% { transform: translate(0, 0) rotate(0deg); }
    100% { transform: translate(-50px, -50px) rotate(360deg); }
}

/* 加载完成后隐藏 */
.loading-screen.hidden {
    opacity: 0;
    visibility: hidden;
    transition: all 0.8s ease-in-out;
}

/* 🎯 鼠标光标效果 */
body {
    cursor: default;
}

.tool-btn, .tool-panel-close, button {
    cursor: pointer !important;
}

.tool-btn:hover {
    cursor: pointer !important;
}

/* 🌟 全局悬浮效果 */
* {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 🎨 选择文本样式 */
::selection {
    background: rgba(64, 224, 255, 0.3);
    color: #fff;
}

::-moz-selection {
    background: rgba(64, 224, 255, 0.3);
    color: #fff;
}

/* 🚀 页面入场动画 */
#cesiumContainer {
    animation: fadeInUp 1s ease-out;
}

@keyframes fadeInUp {
    0% {
        opacity: 0;
        transform: translateY(30px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 🔔 现代化通知系统 */
.notification-container {
    position: fixed;
    top: 100px;
    right: 30px;
    z-index: 10000;
    pointer-events: none;
}

.notification {
    background: linear-gradient(145deg,
        rgba(20, 20, 30, 0.95),
        rgba(30, 30, 45, 0.9)
    );
    border: 1px solid rgba(64, 224, 255, 0.3);
    border-radius: 12px;
    padding: 16px 20px;
    margin-bottom: 12px;
    min-width: 300px;
    max-width: 400px;
    backdrop-filter: blur(20px);
    box-shadow:
        0 10px 30px rgba(0, 0, 0, 0.3),
        0 0 20px rgba(64, 224, 255, 0.1);
    transform: translateX(100%);
    opacity: 0;
    animation: slideInNotification 0.5s ease-out forwards;
    pointer-events: auto;
    position: relative;
    overflow: hidden;
}

.notification::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, #40E0FF, #6495ED);
}

.notification.success::before {
    background: linear-gradient(90deg, #00ff88, #00cc6a);
}

.notification.error::before {
    background: linear-gradient(90deg, #ff5757, #ff3333);
}

.notification.warning::before {
    background: linear-gradient(90deg, #ffaa00, #ff8800);
}

.notification-content {
    color: #fff;
    font-size: 14px;
    line-height: 1.4;
    font-weight: 500;
}

.notification-title {
    font-size: 16px;
    font-weight: 700;
    margin-bottom: 4px;
    color: #40E0FF;
}

.notification.success .notification-title {
    color: #00ff88;
}

.notification.error .notification-title {
    color: #ff5757;
}

.notification.warning .notification-title {
    color: #ffaa00;
}

@keyframes slideInNotification {
    0% {
        transform: translateX(100%);
        opacity: 0;
    }
    100% {
        transform: translateX(0);
        opacity: 1;
    }
}

.notification.hide {
    animation: slideOutNotification 0.3s ease-in forwards;
}

@keyframes slideOutNotification {
    0% {
        transform: translateX(0);
        opacity: 1;
    }
    100% {
        transform: translateX(100%);
        opacity: 0;
    }
}

/* 通用面板基础样式 */
.analysis-panel {
    position: fixed;
    background-color: rgba(255, 255, 255, 0.95);
    border: 1px solid rgba(200, 200, 200, 0.3);
    backdrop-filter: blur(10px);
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
    z-index: 1000;
    display: none;
}

/* 通用输入框样式 */
.analysis-panel input {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    padding: 5px 0px;
    border-radius: 4px;
    outline: none;
}

.analysis-panel input:focus {
    border-color: #00dcff;
    box-shadow: 0 0 5px rgba(0, 220, 255, 0.3);
}

/* 通用标题样式 */
.panel-title {
    font-size: 15px;
    font-weight: bold;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    text-align: center;
    color: #333;
}

/* 通用按钮组样式 */
.button-group {
    margin-top: 15px;
    display: flex;
    gap: 10px;
}



/* 图层切换面板特殊样式 */
#layerPanel {
    left: 20px;
    right: auto;
    top: 100px;
    transform: none;
    width: auto;
    background-color: rgba(255, 255, 255, 0.95);
    border: 1px solid rgba(200, 200, 200, 0.3);
    backdrop-filter: blur(10px);
    padding: 15px;
}

#layerPanel .button-group {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
}

#layerPanel button {
    width: 100%;
    text-align: left;
    padding: 10px 12px;
    margin: 0;
    white-space: nowrap;
    font-size: 13px;
    background-color: rgba(240, 240, 240, 0.9);
    color: #444;
    border: 1px solid rgba(0, 0, 0, 0.1);
    transition: all 0.2s ease;
}

#layerPanel button:hover {
    background-color: rgba(230, 230, 230, 0.95);
    transform: translateX(3px);
}

/* 工具按钮样式已移除 - Widget功能模块不存在 */
#toolButtons {
    display: none; /* 隐藏工具按钮容器 */
}