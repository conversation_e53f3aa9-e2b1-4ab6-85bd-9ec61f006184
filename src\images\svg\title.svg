<?xml version="1.0" encoding="UTF-8"?>
<svg width="400" height="80" viewBox="0 0 400 80" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="titleGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#40E0FF;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#6495ED;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#40E0FF;stop-opacity:1" />
    </linearGradient>
    <filter id="glow">
      <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>
  
  <!-- 背景装饰 -->
  <rect width="400" height="80" fill="url(#titleGradient)" opacity="0.1" rx="10"/>
  
  <!-- 主标题文字 -->
  <text x="200" y="50" text-anchor="middle" font-family="Microsoft YaHei, Arial, sans-serif" 
        font-size="32" font-weight="bold" fill="url(#titleGradient)" filter="url(#glow)">
    电子沙盘
  </text>
  
  <!-- 装饰线条 -->
  <line x1="50" y1="65" x2="150" y2="65" stroke="url(#titleGradient)" stroke-width="2" opacity="0.8"/>
  <line x1="250" y1="65" x2="350" y2="65" stroke="url(#titleGradient)" stroke-width="2" opacity="0.8"/>
  
  <!-- 装饰圆点 -->
  <circle cx="40" cy="40" r="3" fill="#40E0FF" opacity="0.8"/>
  <circle cx="360" cy="40" r="3" fill="#40E0FF" opacity="0.8"/>
  <circle cx="200" cy="15" r="2" fill="#6495ED" opacity="0.6"/>
</svg>
